const { exec } = require('pkg');
const path = require('path');
const fs = require('fs');

async function build() {
  console.log('开始打包...\n');

  try {
    // 确保输出目录存在
    const distDir = path.join(__dirname, 'dist');
    if (!fs.existsSync(distDir)) {
      fs.mkdirSync(distDir, { recursive: true });
    }

    // 打包配置
    const options = [
      'src/scripts/main.js', // 入口文件
      '--target',
      'node18-win-x64', // 目标平台
      '--output',
      'dist/gf-auto-archive.exe', // 输出文件
      '--compress',
      'GZip', // 压缩
    ];

    // 打包 Intel Mac 版本
    const intelOptions = [
      'src/scripts/main.js',
      '--target',
      'node18-macos-x64',
      '--output',
      'dist/gf-auto-archive-mac-intel',
      '--compress',
      'GZip',
    ];

    // 打包 Apple Silicon Mac 版本
    const armOptions = [
      'src/scripts/main.js',
      '--target',
      'node18-macos-arm64',
      '--output',
      'dist/gf-auto-archive-mac-arm64',
      '--compress',
      'GZip',
    ];

    console.log('打包参数:', options.join(' '));
    console.log('\n正在打包，请稍候...');

    // 执行打包
    await exec(options);

    // console.log('正在打包 Intel Mac 版本...');
    // await exec(intelOptions);
    // console.log('✅ Intel Mac 版本打包完成！');

    // console.log('正在打包 Apple Silicon Mac 版本...');
    // await exec(armOptions);
    // console.log('✅ Apple Silicon Mac 版本打包完成！');

    console.log('\n✅ 打包成功！');
    console.log(`输出文件: ${path.join(distDir, 'gf-auto-archive.exe')}`);

    // 复制必要的文件到 dist 目录
    console.log('\n复制配置文件...');

    // 复制 data 目录结构
    const dataSourceDir = path.join(__dirname, 'data');
    const dataTargetDir = path.join(distDir, 'data');

    if (!fs.existsSync(dataTargetDir)) {
      fs.mkdirSync(dataTargetDir, { recursive: true });
      fs.mkdirSync(path.join(dataTargetDir, 'records'), { recursive: true });
    }

    // 如果存在配置文件，复制它
    const configSource = path.join(dataSourceDir, 'config.json');
    const configTarget = path.join(dataTargetDir, 'config.json');

    if (fs.existsSync(configSource)) {
      fs.copyFileSync(configSource, configTarget);
      console.log('已复制: config.json');
    } else {
      // 创建默认配置文件
      const defaultConfig = {
        fetchCount: 5,
        headless: false,
        slowMo: 50,
        timeout: 30000,
        navigationTimeout: 30000,
        retryTimes: 3,
        viewportWidth: 1280,
        viewportHeight: 720,
        logLevel: 'info',
        logToFile: true,
        screenshotOnError: true,
        customSettings: {
          username: '',
          password: '',
          url: '',
          archiveDir: '',
          logDir: '',
          account: '',
          userPassword: '',
        },
      };

      fs.writeFileSync(configTarget, JSON.stringify(defaultConfig, null, 2));
      console.log('已创建默认配置文件');
    }

    // 创建必要的目录
    const logsDir = path.join(distDir, 'logs');
    const screenshotsDir = path.join(distDir, 'screenshots');

    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    if (!fs.existsSync(screenshotsDir)) {
      fs.mkdirSync(screenshotsDir, { recursive: true });
    }

    // 创建使用说明
    const readmeContent = `# 网页自动化脚本工具 使用说明

## 目录结构
- gf-auto-archive.exe  - 主程序
- data/
  - config.json        - 配置文件（可编辑）
  - records/           - 执行记录（自动生成）
- logs/                - 日志文件（自动生成）
- screenshots/         - 截图文件（自动生成）

## 使用方法

### 1. 查看可用脚本
\`\`\`
gf-auto-archive.exe list
\`\`\`

### 2. 运行脚本
\`\`\`
gf-auto-archive.exe run <脚本名>
\`\`\`

### 3. 查看配置
\`\`\`
gf-auto-archive.exe config
\`\`\`

### 4. 重置配置
\`\`\`
gf-auto-archive.exe config-reset
\`\`\`

## 配置说明
编辑 data/config.json 文件可以修改以下配置：
- fetchCount: 每次处理的条数
- headless: 是否无头模式（true/false）
- timeout: 超时时间（毫秒）
- customSettings: 自定义配置（如用户名、密码、网址等）

## 注意事项
1. 首次运行可能需要下载 Chromium 浏览器
2. 确保有足够的磁盘空间存储日志和截图
3. 执行记录会自动保存，避免重复处理
`;

    fs.writeFileSync(path.join(distDir, 'README.txt'), readmeContent);
    console.log('已创建使用说明');

    console.log('\n✅ 打包完成！');
    console.log(`\n输出目录: ${distDir}`);
    console.log('\n包含文件:');
    console.log('  - gf-auto-archive.exe');
    console.log('  - data/config.json');
    console.log('  - README.txt');
    console.log('  - logs/ (目录)');
    console.log('  - screenshots/ (目录)');
  } catch (error) {
    console.error('\n❌ 打包失败:', error);
    process.exit(1);
  }
}

// 运行打包
build();
