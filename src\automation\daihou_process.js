const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');
const { tabGoPage, goBack } = require('../utils/navigation');
const { downloadPDFFile } = require('../utils/pdfDownloader');

// 获取账款到期日调整&融资到期日调整
async function getZiChanDaoQiDetail(page, detailButton, dirs) {
  try {
    await detailButton.click();
    await page.waitForLoadState('networkidle', {
      timeout: 15000,
    });
    await page.waitForTimeout(3000);

    // 获取表格的所有行
    const tableRows = page.locator(
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div[4]/div/form/div/div/div[3]/table/tbody/tr',
    );
    const rowCount = await tableRows.count();

    if (rowCount === 0) {
      logger.info('没有找到到期日调整数据');
      return;
    }

    // 遍历每一行
    for (let i = 0; i < rowCount; i++) {
      const row = tableRows.nth(i);

      try {
        // 获取当前行的文件链接（第4列）
        const fileElement = row.locator('td').nth(3).locator('a');
        const fileCount = await fileElement.count();

        // 获取当前行的合同编号（第3列）
        const contractNoElement = row.locator('td').nth(2).locator('div');
        const contractNoCount = await contractNoElement.count();

        if (fileCount === 0 || contractNoCount === 0) {
          logger.warn(`第 ${i + 1} 行缺少文件链接或合同编号，跳过`);
          continue;
        }

        const contractNo = await contractNoElement.textContent();
        const contractNoFormat = contractNo.trim();

        // 先下载到 daoQiRiDir 目录
        const downloadSuccess = await downloadPDFFile(
          fileElement,
          page,
          dirs.daoQiRiDir,
          `到期日调整申请书-${contractNoFormat}`,
        );

        // 如果下载成功，复制文件到 rongZiDaoQiRiDir 目录并重命名
        if (downloadSuccess) {
          const sourceFilePath = path.join(
            dirs.daoQiRiDir,
            `到期日调整申请书-${contractNoFormat}.pdf`,
          );
          const targetFilePath = path.join(
            dirs.rongZiDaoQiRiDir,
            `融资到期日调整申请书-${contractNoFormat}.pdf`,
          );

          fs.copyFileSync(sourceFilePath, targetFilePath);
        } else {
          logger.warn(`第 ${i + 1} 行文件下载失败：${contractNoFormat}`);
        }
      } catch (rowError) {
        logger.error(`处理第 ${i + 1} 行时出错: ${rowError.message}`);
        continue; // 继续处理下一行
      }
    }

    logger.success(`到期日调整文件处理完成，共处理 ${rowCount} 行`);
  } catch (error) {
    logger.error(`获取到期日调整详情失败: ${error.message}`);
  }
}

// 反转让
async function getFanZhuanRang(page, detailButton, dirs) {
  try {
    await detailButton.click();
    await page.waitForLoadState('networkidle', {
      timeout: 15000,
    });
    await page.waitForTimeout(3000);

    // 获取表格的所有行
    const tableRows = page.locator(
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[4]/div/form/div/div/div[3]/table/tbody/tr',
    );
    const rowCount = await tableRows.count();

    logger.info(`找到 ${rowCount} 行反转让数据`);

    if (rowCount === 0) {
      logger.info('没有找到反转让数据');
      return;
    }

    // 遍历每一行
    for (let i = 0; i < rowCount; i++) {
      const row = tableRows.nth(i);

      try {
        // 获取当前行的文件链接（第4列）
        const fileElement = row.locator('td').nth(3).locator('a');
        const fileCount = await fileElement.count();

        // 获取当前行的合同编号（第3列）
        const contractNoElement = row.locator('td').nth(2).locator('div');
        const contractNoCount = await contractNoElement.count();

        if (fileCount === 0 || contractNoCount === 0) {
          logger.warn(`第 ${i + 1} 行缺少文件链接或合同编号，跳过`);
          continue;
        }

        const contractNo = await contractNoElement.textContent();
        const contractNoFormat = contractNo.trim();

        logger.info(`处理第 ${i + 1} 行：合同编号 ${contractNoFormat}`);

        await downloadPDFFile(
          fileElement,
          page,
          dirs.fanZhuanRangDir,
          `应收帐款反转让通知书-${contractNoFormat}`,
        );
      } catch (rowError) {
        logger.error(`处理第 ${i + 1} 行时出错: ${rowError.message}`);
        continue;
      }
    }

    logger.success(`反转让文件处理完成，共处理 ${rowCount} 行`);
  } catch (error) {
    logger.error(`获取反转让详情失败: ${error.message}`);
  }
}

// 账款跟踪
async function getZKGZDetail(page, detailButton, dirs) {
  try {
    await detailButton.click();
    await page.waitForLoadState('networkidle', {
      timeout: 15000,
    });
    await page.waitForTimeout(3000);

    // 获取表格的所有行 - 文件表格
    const fileTableRows = page.locator(
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/div[3]/div/div/div/div/div[3]/table/tbody/tr',
    );
    const fileRowCount = await fileTableRows.count();

    // 获取表格的所有行 - 合同编号表格
    const contractTableRows = page.locator(
      'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/div[4]/div/form/div/div/div[3]/table/tbody/tr',
    );
    const contractRowCount = await contractTableRows.count();

    if (fileRowCount === 0 || contractRowCount === 0) {
      logger.info('没有找到账款跟踪数据');
      return;
    }

    // 取较小的行数作为处理数量
    const processRowCount = Math.min(fileRowCount, contractRowCount);

    // 遍历每一行
    for (let i = 0; i < processRowCount; i++) {
      try {
        // 获取当前行的文件链接
        const fileRow = fileTableRows.nth(i);
        const fileElement = fileRow.locator('td').nth(2).locator('div/div/a');
        const fileCount = await fileElement.count();

        // 获取当前行的合同编号
        const contractRow = contractTableRows.nth(i);
        const contractNoElement = contractRow
          .locator('td')
          .nth(2)
          .locator('div');
        const contractNoCount = await contractNoElement.count();

        if (fileCount === 0 || contractNoCount === 0) {
          logger.warn(`第 ${i + 1} 行缺少文件链接或合同编号，跳过`);
          continue;
        }

        const contractNo = await contractNoElement.textContent();
        const contractNoFormat = contractNo.trim();

        await downloadPDFFile(
          fileElement,
          page,
          dirs.zhangKuanGenZongDir,
          `1-应收帐款转让通知函-${contractNoFormat}`,
        );
      } catch (rowError) {
        logger.error(`处理第 ${i + 1} 行时出错: ${rowError.message}`);
        continue;
      }
    }

    logger.success(`账款跟踪文件处理完成，共处理 ${processRowCount} 行`);
  } catch (error) {
    logger.error(`获取账款跟踪详情失败: ${error.message}`);
  }
}

// 提取查询逻辑为独立方法
async function performDaoQiRiSearch(page, customerName, type) {
  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await page.waitForTimeout(3000);

  // /html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div/div[1]/div[2]/div/div/div

  const locatorMap = {
    dqr: 'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div/div[1]/div[3]/div/div/div[1]',
    fzr: 'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div/div[1]/div[2]/div/div/div[1]',
    zkgz: 'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div/div[1]/div[2]/div/div/div[1]',
  };

  const locator = locatorMap[type];
  const customerNameSearchInput = page.locator(locator);

  await customerNameSearchInput.click();
  const inputDom = customerNameSearchInput.locator('input');
  await inputDom.fill(customerName);
  await inputDom.press('Enter');

  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await page.getByRole('listitem').filter({ hasText: customerName }).click();

  await page.getByRole('button', { name: '查询' }).click();
  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await page.waitForTimeout(3000);
}

/* 
  @param type: dqr = 到期日调整 fzr = 反转让通知
*/
async function getZiChanDaoQi(page, type, customerName, dirInfo) {
  const typeNameMap = {
    dqr: '资产到期日调整',
    fzr: '反转让调整',
    zkgz: '帐款跟踪',
  };
  logger.info(`开始获取${typeNameMap[type]}`);

  try {
    const targetUrlMap = {
      dqr: 'https://gfconsole-wsuat.syitservice.com/web-tenant/index.html#/assetsCenter/dueDateAdjust',
      fzr: 'https://gfconsole-wsuat.syitservice.com/web-tenant/index.html#/assetsCenter/atAccount',
      zkgz: 'https://gfconsole-wsuat.syitservice.com/web-tenant/index.html#/assetsCenter/assetsTracking',
    };
    await tabGoPage(page, targetUrlMap[type]);

    // 执行查询逻辑
    await performDaoQiRiSearch(page, customerName, type);

    // 查找表格中的详情按钮
    const detailButtons = page.getByRole('button', { name: '详情' });
    const count = await detailButtons.count();

    logger.info(`找到了 ${count} 条`);

    for (let i = 0; i < count; i++) {
      // 实测不需要重新查询，返回列表页时，查询结果有保留
      // 重新执行查询以确保页面状态正确
      // if (i > 0) {
      //   await performDaoQiRiSearch(page, customerName, type);
      // }
      // 重新获取按钮并点击第i个
      // const currentButtons = page.getByRole('button', {
      //   name: '详情',
      // });
      const btn = await detailButtons.nth(i);
      if (type === 'dqr') {
        await getZiChanDaoQiDetail(page, btn, dirInfo);
      } else if (type === 'fzr') {
        await getFanZhuanRang(page, btn, dirInfo);
      } else if (type === 'zkgz') {
        await getZKGZDetail(page, btn, dirInfo);
      }
      await goBack(page);
    }
    logger.success(`${typeNameMap[type]}收集成功`);
  } catch (error) {
    console.error(error.message);
    logger.error(`获取到期日调整通知书失败： ${error.message}`);
  }
}

module.exports = {
  getZiChanDaoQi,
};
