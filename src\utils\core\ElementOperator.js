const logger = require('../logger');

/**
 * 元素操作类 - 统一处理页面元素交互
 */
class ElementOperator {
  constructor(page, options = {}) {
    this.page = page;
    this.options = {
      timeout: 10000,
      retryCount: 3,
      retryDelay: 1000,
      ...options,
    };
  }

  /**
   * 智能点击元素
   * @param {string|Array} selector - 选择器或选择器数组
   * @param {Object} options - 选项
   */
  async click(selector, options = {}) {
    const selectors = Array.isArray(selector) ? selector : [selector];
    const opts = { ...this.options, ...options };

    for (const sel of selectors) {
      try {
        const element = this.page.locator(sel);
        await element.waitFor({ state: 'visible', timeout: opts.timeout });
        await element.click();
        return true;
      } catch (error) {
        logger.warn(`选择器失败: ${sel}, 尝试下一个`);
        continue;
      }
    }

    throw new Error(`所有选择器都失败: ${selectors.join(', ')}`);
  }

  /**
   * 填写输入框
   * @param {string} selector - 选择器
   * @param {string} value - 输入值
   */
  async fillInput(selector, value, options = {}) {
    const opts = { ...this.options, ...options };

    try {
      const input = this.page.locator(selector);
      await input.waitFor({ state: 'visible', timeout: opts.timeout });
      await input.clear();
      await input.fill(value);
    } catch (error) {
      logger.error(`填写输入框失败: ${selector}`, error);
      throw error;
    }
  }

  /**
   * 获取元素文本内容
   * @param {string|Array} selector - 选择器或选择器数组
   */
  async getText(selector, options = {}) {
    const selectors = Array.isArray(selector) ? selector : [selector];
    const opts = { ...this.options, ...options };

    for (const sel of selectors) {
      try {
        const element = this.page.locator(sel);
        await element.waitFor({ state: 'visible', timeout: opts.timeout });
        const text = await element.textContent();
        return text?.trim() || '';
      } catch (error) {
        logger.warn(`获取文本失败: ${sel}, 尝试下一个`);
        continue;
      }
    }

    throw new Error(`所有选择器都无法获取文本: ${selectors.join(', ')}`);
  }

  /**
   * 等待元素出现
   * @param {string} selector - 选择器
   */
  async waitForElement(selector, options = {}) {
    const opts = { ...this.options, ...options };

    try {
      await this.page.locator(selector).waitFor({
        state: 'visible',
        timeout: opts.timeout,
      });
    } catch (error) {
      logger.error(`等待元素超时: ${selector}`, error);
      throw error;
    }
  }

  /**
   * 等待查询结果加载
   */
  async waitForResults(options = {}) {
    const opts = { ...this.options, ...options };

    try {
      await this.page.waitForLoadState('networkidle', {
        timeout: opts.timeout,
      });
      logger.success('查询结果已加载');
    } catch (error) {
      logger.error('等待查询结果超时', error);
      throw error;
    }
  }
}

module.exports = ElementOperator;
