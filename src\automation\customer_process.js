const logger = require('../utils/logger');
const { downloadFileFromURL } = require('../utils/pdfDownloader');
const SELECTORS = require('../utils/config/selectors');

async function openCustomerDetail(page, detailPageUrl) {
  // 如果提供了详情页面URL，先跳转到该页面
  if (detailPageUrl) {
    const currentUrl = page.url();
    if (currentUrl !== detailPageUrl) {
      logger.info('跳转到详情页面...');
      await page.goto(detailPageUrl, {
        timeout: 15000,
        waitUntil: 'domcontentloaded',
      });
      await page.waitForLoadState('networkidle', { timeout: 10000 });
      await page.waitForTimeout(2000);
      logger.info('已跳转到详情页面');
    }
  }
  const newPagePromise = page.context().waitForEvent('page');
  const customer = page.locator(SELECTORS.detail.customerName);
  await customer.click();
  const customerPage = await newPagePromise;

  // 等待页面加载
  await customerPage.waitForLoadState('networkidle', { timeout: 15000 });
  // 额外等待页面渲染
  await customerPage.waitForTimeout(2000);

  return customerPage;
}

async function getLicense(customerPage, downloadDir, customerName) {
  try {
    // 找到包含"营业执照"文本的标签
    const licenseLabel = customerPage
      .locator('#iframeContainer')
      .contentFrame()
      .getByText('营业执照', { exact: true });
    await licenseLabel.waitFor({ state: 'visible', timeout: 10000 });

    // 从标签向上找到整个表单项容器
    const formItem = licenseLabel.locator(
      'xpath=ancestor::div[contains(@class, "el-form-item")]',
    );
    await formItem.waitFor({ state: 'visible', timeout: 5000 });

    // 在表单项容器中查找链接
    const licenseEl = formItem.locator('a[href*="getFile"]');
    const licenseCount = await licenseEl.count();

    if (licenseCount === 0) {
      logger.info(`${customerName} 没有营业执照`);
      // 关闭页面
      await customerPage.close();
      return true;
    }

    const licenseElement = licenseEl.nth(0);
    const href = await licenseElement.getAttribute('href');

    // 使用新的 downloadFile 函数，支持 PNG 等图片格式
    // 注意：这里传入的是 iframePage，因为元素在 iframe 页面中
    const success = await downloadFileFromURL(
      href,
      customerPage,
      downloadDir,
      `【营业执照】${customerName}`,
      {
        waitTime: 5000,
        fileType: 'png',
      },
    );

    // 关闭页面
    // await customerPage.close();
    return success;
  } catch (error) {
    logger.error(`获取营业执照失败: ${error.message}`);
    // if (iframePage) await iframePage.close();
    return false;
  }
}

// 获取法人身份证照片
async function getLegalIdCard(customerPage, downloadDir) {
  logger.info('获取法人身份证照片');
  // 监听新页面创建

  await customerPage
    .locator('#iframeContainer')
    .contentFrame()
    .locator('div')
    .filter({ hasText: /^主要成员$/ })
    .click();
  await customerPage
    .locator('#iframeContainer')
    .contentFrame()
    .getByRole('menuitem', { name: '主要成员', exact: true })
    .click();

  await customerPage.waitForLoadState('networkidle', { timeout: 15000 });
  await customerPage.waitForTimeout(3000);

  // 法定代表人这一行的 详情按钮
  // page.locator('#iframeContainer').contentFrame().getByRole('cell', { name: '法定代表人' }).click();

  // await page.locator('#iframeContainer').contentFrame().locator('.el-form > div > .el-form-item__content').first().click();

  // await page.locator('#iframeContainer').contentFrame().locator('.el-form > div:nth-child(2) > .el-form-item__content').first().click();

  // 找到法定代表人单元格

  const table = customerPage
    .locator('#iframeContainer')
    .contentFrame()
    .locator('div.el-table__body-wrapper');

  const trList = table.locator('tr.el-table__row');

  const trCount = await trList.count();
  let legalPersonName = null;
  let legalTrIndex = -1;

  for (let i = 0; i < trCount; i++) {
    const trText = await trList.nth(i).textContent();
    if (trText.includes('法定代表人')) {
      legalTrIndex = i;
      legalPersonName = await trList.nth(i).locator('td').nth(5).textContent();
      logger.info(`法定代表人：${legalPersonName}`);
    }
  }

  const fixedRightTableTrList = customerPage
    .locator('#iframeContainer')
    .contentFrame()
    .locator('div.el-table__fixed-right .el-table__fixed-body-wrapper tr');

  const detailBtn = fixedRightTableTrList
    .nth(legalTrIndex)
    .getByRole('button', { name: '详情' });

  await detailBtn.click();

  // 等待详情页面加载
  await customerPage.waitForLoadState('networkidle', { timeout: 15000 });
  await customerPage.waitForTimeout(1500);

  // 获取身份证正面附件
  try {
    const frontIdLabel = customerPage
      .locator('#iframeContainer')
      .contentFrame()
      .getByText('管理员身份证正面', { exact: true });

    const frontIdFormItem = frontIdLabel.locator(
      'xpath=ancestor::div[contains(@class, "el-form-item")]',
    );
    const frontIdLink = frontIdFormItem.locator('a[href*="getFile"]');

    const frontIdCount = await frontIdLink.count();
    if (frontIdCount > 0) {
      const frontIdHref = await frontIdLink.getAttribute('href');
      await downloadFileFromURL(
        frontIdHref,
        customerPage,
        downloadDir,
        `【身份证正面】法人-${legalPersonName.trim()}`,
        {
          waitTime: 5000,
          fileType: 'jpg',
        },
      );
    } else {
      logger.info('未找到身份证正面附件');
    }
  } catch (error) {
    logger.warn(`获取身份证正面失败: ${error.message}`);
  }

  // 获取身份证反面附件
  try {
    const backIdLabel = customerPage
      .locator('#iframeContainer')
      .contentFrame()
      .getByText('管理员身份证反面', { exact: true });

    const backIdFormItem = backIdLabel.locator(
      'xpath=ancestor::div[contains(@class, "el-form-item")]',
    );
    const backIdLink = backIdFormItem.locator('a[href*="getFile"]');

    const backIdCount = await backIdLink.count();
    if (backIdCount > 0) {
      const backIdHref = await backIdLink.getAttribute('href');
      await downloadFileFromURL(
        backIdHref,
        customerPage,
        downloadDir,
        `【身份证反面】法人-${legalPersonName.trim()}`,
        {
          waitTime: 5000,
          fileType: 'jpg',
        },
      );
    } else {
      logger.info('未找到身份证反面附件');
    }
  } catch (error) {
    logger.warn(`获取身份证反面失败: ${error.message}`);
  }
}

async function getCustomerBasicInfo(
  page,
  downloadDir,
  customerName,
  detailPageUrl,
) {
  const customerPage = await openCustomerDetail(page, detailPageUrl);
  await getLicense(customerPage, downloadDir, customerName);
  await getLegalIdCard(customerPage, downloadDir);
  await customerPage.close();
}

module.exports = {
  getCustomerBasicInfo,
};
