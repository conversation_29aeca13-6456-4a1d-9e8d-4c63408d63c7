const winston = require('winston');
const path = require('path');
const fs = require('fs');
const config = require('./config');

/**
 * 操作记录器 - 详细记录自动化操作过程
 * 使用 winston 进行结构化日志记录
 */
class OperationLogger {
  constructor(scriptName) {
    this.scriptName = scriptName;
    this.sessionId = this.generateSessionId();
    this.configInstance = config.getInstance();
    this.initLogger();
  }

  /**
   * 生成会话ID
   */
  generateSessionId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取日志目录
   */
  getLogDirectory() {
    // 优先使用配置中的 logDir
    const configLogDir = this.configInstance.get('customSettings.logDir');
    if (configLogDir) {
      return path.join(configLogDir, 'operations');
    }

    // 如果配置了 archiveDir，在其下创建 logs 目录
    const archiveDir = this.configInstance.get('customSettings.archiveDir');
    if (archiveDir) {
      return path.join(archiveDir, 'logs', 'operations');
    }

    // 默认使用项目根目录下的 logs
    return path.join(__dirname, '../../logs/operations');
  }

  /**
   * 初始化日志器
   */
  initLogger() {
    const logDir = this.getLogDirectory();

    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // 可读格式 - 用于调试
    const readableFormat = winston.format.combine(
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        let output = `${timestamp} [${level.toUpperCase()}] ${message}`;
        if (Object.keys(meta).length > 0) {
          output += `\n  ${JSON.stringify(meta, null, 2)}`;
        }
        return output;
      }),
    );

    this.logger = winston.createLogger({
      level: 'debug',
      defaultMeta: {
        scriptName: this.scriptName,
        sessionId: this.sessionId,
        logDir: logDir,
      },
      transports: [
        // 可读格式日志 - 用于调试
        new winston.transports.File({
          filename: path.join(logDir, `${this.scriptName}-operations.log`),
          format: readableFormat,
          maxsize: 10485760,
          maxFiles: 5,
        }),
      ],
    });

    // 记录日志目录信息
    this.logger.info('操作日志器初始化完成', {
      event: 'logger_init',
      logDirectory: logDir,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 记录脚本开始
   */
  logScriptStart(metadata = {}) {
    this.logger.info('脚本执行开始', {
      event: 'script_start',
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }

  /**
   * 记录脚本结束
   */
  logScriptEnd(success, duration, metadata = {}) {
    this.logger.info('脚本执行结束', {
      event: 'script_end',
      success,
      duration,
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }

  /**
   * 记录登录操作
   */
  logLogin(success, account, error = null) {
    this.logger.info('登录操作', {
      event: 'login',
      success,
      account,
      error: error?.message,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 记录页面导航
   */
  logNavigation(url, success, loadTime = null) {
    this.logger.info('页面导航', {
      event: 'navigation',
      url,
      success,
      loadTime,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 记录查询操作
   */
  logQuery(withdrawalNumber, success, duration, error = null) {
    this.logger.info('查询操作', {
      event: 'query',
      withdrawalNumber,
      success,
      duration,
      error: error?.message,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 记录数据提取
   */
  logDataExtraction(data, success) {
    this.logger.info('数据提取', {
      event: 'data_extraction',
      data,
      success,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 记录文件下载
   */
  logFileDownload(fileName, filePath, success, fileSize = null) {
    this.logger.info('文件下载', {
      event: 'file_download',
      fileName,
      filePath,
      success,
      fileSize,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 记录错误
   */
  logError(operation, error, context = {}) {
    this.logger.error('操作错误', {
      event: 'error',
      operation,
      error: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 记录性能指标
   */
  logPerformance(operation, duration, metadata = {}) {
    this.logger.info('性能指标', {
      event: 'performance',
      operation,
      duration,
      ...metadata,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 记录批量处理结果
   */
  logBatchResult(results) {
    this.logger.info('批量处理结果', {
      event: 'batch_result',
      ...results,
      timestamp: new Date().toISOString(),
    });
  }
}

module.exports = OperationLogger;
