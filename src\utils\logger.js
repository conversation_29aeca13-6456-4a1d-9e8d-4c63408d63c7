const winston = require('winston');
const path = require('path');
const fs = require('fs');
const chalk = require('chalk');
const ora = require('ora');

// 获取日志目录 - 支持配置化
function getLogDirectory() {
  try {
    const config = require('./config');
    const configInstance = config.getInstance();

    // 优先使用配置中的 logDir
    const configLogDir = configInstance.get('customSettings.logDir');
    if (configLogDir) {
      return configLogDir;
    }

    // 如果配置了 archiveDir，在其下创建 logs 目录
    const archiveDir = configInstance.get('customSettings.archiveDir');
    if (archiveDir) {
      return path.join(archiveDir, 'logs');
    }
  } catch (error) {
    // 配置加载失败时使用默认目录
    console.warn('无法加载配置，使用默认日志目录');
  }

  // 默认使用项目根目录下的 logs
  return path.join(__dirname, '../../logs');
}

// 确保日志目录存在
const logDir = getLogDirectory();
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 自定义日志格式
const customFormat = winston.format.printf(
  ({ level, message, timestamp, ...metadata }) => {
    let msg = `${timestamp} [${level.toUpperCase()}] ${message}`;

    // 如果有额外的元数据，也打印出来
    if (Object.keys(metadata).length > 0) {
      msg += ` ${JSON.stringify(metadata)}`;
    }

    return msg;
  },
);

// 自定义控制台格式 - 使用chalk增强颜色
const consoleFormat = winston.format.printf(
  ({ level, message, timestamp, ...metadata }) => {
    const time = chalk.gray(timestamp);
    let icon = '';
    let coloredMessage = message;

    // 根据日志级别设置图标和颜色
    switch (level) {
      case 'info':
        icon = chalk.blue('ℹ');
        break;
      case 'warn':
        icon = chalk.yellow('⚠');
        coloredMessage = chalk.yellow(message);
        break;
      case 'error':
        icon = chalk.red('✖');
        coloredMessage = chalk.red(message);
        break;
      case 'debug':
        icon = chalk.magenta('🔍');
        coloredMessage = chalk.magenta(message);
        break;
      default:
        icon = chalk.blue('ℹ');
    }

    let output = `${time} ${icon} ${coloredMessage}`;

    // 如果有额外的元数据，也打印出来
    if (Object.keys(metadata).length > 0) {
      output += chalk.gray(` ${JSON.stringify(metadata)}`);
    }

    return output;
  },
);

// 创建 Winston logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    customFormat,
  ),
  transports: [
    // 控制台输出 - 使用chalk增强颜色
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        consoleFormat,
      ),
    }),
    // 文件输出保持不变
    new winston.transports.File({
      filename: path.join(logDir, 'app.log'),
      maxsize: 5242880,
      maxFiles: 5,
    }),
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880,
      maxFiles: 5,
    }),
  ],
});

// 全局spinner实例管理
let currentSpinner = null;

// 格式化时间戳（仅在需要时使用）
const getTimestamp = () => chalk.gray(new Date().toLocaleString());

// 扩展日志方法，使用chalk和ora提升用户体验
const loggerWrapper = {
  // 基础日志方法 - 只使用winston输出，避免重复
  info: (message, meta = {}) => {
    if (currentSpinner) currentSpinner.stop();
    logger.info(message, meta);
    if (currentSpinner) currentSpinner.start();
  },

  error: (message, error = null) => {
    if (currentSpinner) currentSpinner.stop();

    let errorDetails = {};
    if (error instanceof Error) {
      errorDetails = {
        error: error.message,
        stack: error.stack,
      };
      logger.error(message, errorDetails);
      // 对于Error对象，额外显示堆栈信息
      if (error.stack) {
        console.log(
          chalk.red(`   ${error.stack.split('\n').slice(1, 3).join('\n   ')}`),
        );
      }
    } else if (error) {
      errorDetails = { error };
      logger.error(message, errorDetails);
    } else {
      logger.error(message);
    }

    if (currentSpinner) currentSpinner.start();
  },

  warn: (message, meta = {}) => {
    if (currentSpinner) currentSpinner.stop();
    logger.warn(message, meta);
    if (currentSpinner) currentSpinner.start();
  },

  debug: (message, meta = {}) => {
    if (currentSpinner) currentSpinner.stop();
    logger.debug(message, meta);
    if (currentSpinner) currentSpinner.start();
  },

  // 成功日志 - 通过winston输出，带特殊标记
  success: (message, meta = {}) => {
    if (currentSpinner) currentSpinner.stop();
    logger.info(`✅ ${message}`, meta);
    if (currentSpinner) currentSpinner.start();
  },

  // 脚本开始 - 使用装饰线和winston
  scriptStart: (scriptName) => {
    if (currentSpinner) currentSpinner.stop();

    // 使用console.log显示装饰性分隔线（不是日志内容）
    const separator = chalk.cyan('═'.repeat(60));
    console.log('\n' + separator);

    // 实际日志通过winston输出
    logger.info(`🚀 脚本 [${scriptName}] 开始执行`);

    console.log(separator + '\n');
  },

  // 脚本结束 - 增强视觉效果
  scriptEnd: (scriptName, success = true, duration = null) => {
    if (currentSpinner) currentSpinner.stop();

    const status = success ? '成功' : '失败';
    const emoji = success ? '✅' : '❌';
    const color = success ? chalk.green : chalk.red;

    // 装饰性分隔线
    const separator = color('═'.repeat(60));
    console.log('\n' + separator);

    // 实际日志通过winston输出
    const logMessage = `${emoji} 脚本 [${scriptName}] 执行${status}${
      duration ? ` (耗时: ${duration}ms)` : ''
    }`;
    logger[success ? 'info' : 'error'](logMessage);

    console.log(separator + '\n');
  },

  // 步骤记录 - 通过winston输出
  step: (stepName, stepNumber = null) => {
    if (currentSpinner) currentSpinner.stop();

    const prefix = stepNumber ? `[步骤 ${stepNumber}]` : '[步骤]';
    logger.info(`📋 ${prefix} ${stepName}`);

    if (currentSpinner) currentSpinner.start();
  },

  // 数据处理记录 - 通过winston输出
  data: (action, count, details = {}) => {
    if (currentSpinner) currentSpinner.stop();
    logger.info(`📊 数据${action}: ${count}条`, details);
    if (currentSpinner) currentSpinner.start();
  },

  // Spinner相关方法
  startSpinner: (text, options = {}) => {
    if (currentSpinner) {
      currentSpinner.stop();
    }

    currentSpinner = ora({
      text: chalk.blue(text),
      color: 'blue',
      spinner: 'dots',
      ...options,
    }).start();

    return currentSpinner;
  },

  updateSpinner: (text) => {
    if (currentSpinner) {
      currentSpinner.text = chalk.blue(text);
    }
  },

  succeedSpinner: (text) => {
    if (currentSpinner) {
      currentSpinner.succeed(chalk.green(text));
      currentSpinner = null;
    }
  },

  failSpinner: (text) => {
    if (currentSpinner) {
      currentSpinner.fail(chalk.red(text));
      currentSpinner = null;
    }
  },

  stopSpinner: () => {
    if (currentSpinner) {
      currentSpinner.stop();
      currentSpinner = null;
    }
  },

  // 进度相关方法 - 这是UI展示，不是日志，保留console.log
  progress: (current, total, message = '') => {
    if (currentSpinner) currentSpinner.stop();

    const percentage = Math.round((current / total) * 100);
    const progressBar =
      '█'.repeat(Math.floor(percentage / 5)) +
      '░'.repeat(20 - Math.floor(percentage / 5));
    const progressText = `${chalk.cyan(progressBar)} ${chalk.bold(
      `${current}/${total}`,
    )} ${chalk.gray(`(${percentage}%)`)}`;

    // 进度条是UI展示，不是日志记录，使用console.log合适
    console.log(`${getTimestamp()} 📈 ${progressText} ${message}`);

    if (currentSpinner) currentSpinner.start();
  },

  // 表格数据显示 - 这是UI展示，不是日志，保留console.log
  table: (data, title = '') => {
    if (currentSpinner) currentSpinner.stop();

    if (title) {
      console.log(`\n${chalk.blue.bold('📋 ' + title)}`);
      console.log(chalk.blue('─'.repeat(title.length + 3)));
    }

    if (Array.isArray(data)) {
      data.forEach((item, index) => {
        console.log(`${chalk.gray(`${index + 1}.`)} ${item}`);
      });
    } else if (typeof data === 'object') {
      Object.entries(data).forEach(([key, value]) => {
        console.log(`${chalk.blue(key)}: ${chalk.white(value)}`);
      });
    }

    console.log('');

    if (currentSpinner) currentSpinner.start();
  },

  // 分隔线 - UI装饰元素，保留console.log
  separator: (char = '─', length = 50, color = 'gray') => {
    if (currentSpinner) currentSpinner.stop();
    console.log(chalk[color](char.repeat(length)));
    if (currentSpinner) currentSpinner.start();
  },

  // 清屏 - UI操作，保留console.clear
  clear: () => {
    if (currentSpinner) currentSpinner.stop();
    console.clear();
  },
};

module.exports = loggerWrapper;
