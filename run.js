#!/usr/bin/env node

const path = require('path');
const fs = require('fs');

// 获取命令行参数
const args = process.argv.slice(2);

// 查找 -p 参数
const paramIndex = args.indexOf('-p');
if (paramIndex === -1 || paramIndex === args.length - 1) {
  console.error('错误: 请使用 -p 参数指定脚本文件');
  console.error('示例: pnpm run script -p example.js');
  console.error('     或: pnpm run script -p table-fetch.js');
  process.exit(1);
}

// 获取脚本文件名
const scriptFile = args[paramIndex + 1];
// 如果没有 .js 后缀，添加它
const scriptFileName = scriptFile.endsWith('.js')
  ? scriptFile
  : scriptFile + '.js';
// 构建完整路径
const fullScriptPath = path.join(__dirname, 'src/scripts', scriptFileName);

// 检查文件是否存在
if (!fs.existsSync(fullScriptPath)) {
  console.error(`错误: 脚本文件不存在: ${fullScriptPath}`);
  console.error(`请检查文件名是否正确: ${scriptFileName}`);

  // 列出可用的脚本
  const scriptsDir = path.join(__dirname, 'src/scripts');
  if (fs.existsSync(scriptsDir)) {
    const scripts = fs
      .readdirSync(scriptsDir)
      .filter((file) => file.endsWith('.js'));

    if (scripts.length > 0) {
      console.log('\n可用的脚本:');
      scripts.forEach((script) => console.log(`  - ${script}`));
    }
  }

  process.exit(1);
}

// 设置环境变量，让脚本知道是通过运行器启动的
process.env.SCRIPT_RUNNER = 'true';
process.env.SCRIPT_NAME = path.basename(scriptFileName);

// 运行脚本
console.log(`运行脚本: ${scriptFileName}\n`);
require(fullScriptPath);
