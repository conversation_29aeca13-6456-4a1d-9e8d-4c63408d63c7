const { createBrowser, createPage, closeBrowser } = require('../utils/browser');
const logger = require('../utils/logger');
const config = require('../utils/config');
const ElementOperator = require('../utils/core/ElementOperator');
const NavigationManager = require('../utils/core/NavigationManager');
const FileManager = require('../utils/core/FileManager');

// 导入自动化模块
const LoginManager = require('../automation/LoginManager');
const WithdrawalProcessor = require('../automation/WithdrawalProcessor');
const UserInteractionManager = require('../automation/UserInteractionManager');
const OperationLogger = require('../utils/OperationLogger');

/**
 * 主自动化脚本
 */
async function run() {
  const configInstance = config.getInstance();
  const operationLogger = new OperationLogger('main');
  const userInteraction = new UserInteractionManager();

  let browser = null;
  let page = null;
  const startTime = Date.now();

  try {
    operationLogger.logScriptStart();

    browser = await createBrowser();
    page = await createPage(browser);

    const elementOp = new ElementOperator(page);
    const navigationManager = new NavigationManager(page);
    const fileManager = new FileManager();

    const loginManager = new LoginManager(
      elementOp,
      navigationManager,
      configInstance,
    );
    await loginManager.performLogin();
    operationLogger.logLogin(
      true,
      configInstance.get('customSettings.account'),
    );

    const userInput = await userInteraction.getUserInput();
    if (!userInput) {
      logger.warn('用户未输入任何提款申请号，脚本结束');
      return;
    }

    const withdrawalNumbers = userInteraction.parseWithdrawalNumbers(userInput);
    if (withdrawalNumbers.length === 0) {
      logger.warn('没有有效的提款申请号，脚本结束');
      return;
    }

    // 5. 处理提款申请号查询
    const processor = new WithdrawalProcessor(
      elementOp,
      navigationManager,
      fileManager,
      configInstance,
    );
    const results = await processor.processWithdrawalNumbers(withdrawalNumbers);

    operationLogger.logBatchResult(results);
    logger.success('所有查询任务执行完成');

    // 6. 用户确认关闭浏览器
    // await userInteraction.confirmBrowserClose();

    const duration = Date.now() - startTime;
    operationLogger.logScriptEnd(true, duration);
  } catch (error) {
    logger.error('脚本执行失败', error);
    operationLogger.logError('script_execution', error);

    // 保存错误截图
    if (page) {
      try {
        const errorScreenshotPath = `screenshots/error_${Date.now()}.png`;
        await page.screenshot({ path: errorScreenshotPath, fullPage: true });
        logger.info('已保存错误截图', { path: errorScreenshotPath });
      } catch (screenshotError) {
        logger.error('保存错误截图失败', screenshotError);
      }
    }

    // 错误情况下询问用户是否关闭浏览器
    try {
      await userInteraction.confirmBrowserCloseOnError();
    } catch (confirmError) {
      logger.warn('用户确认过程出错，将自动关闭浏览器', confirmError);
    }

    const duration = Date.now() - startTime;
    operationLogger.logScriptEnd(false, duration, { error: error.message });
    throw error;
  } finally {
    // 只有在用户确认关闭或出现错误时才关闭浏览器
    if (!global.keepBrowserOpen) {
      await closeBrowser(browser);
    } else {
      logger.info('根据用户选择，浏览器保持打开状态');
    }
  }
}

module.exports = { run };

if (require.main === module || process.env.SCRIPT_RUNNER === 'true') {
  run().catch((error) => {
    logger.error('脚本运行失败', error);
    process.exit(1);
  });
}
