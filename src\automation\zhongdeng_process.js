const logger = require('../utils/logger');
const SELECTORS = require('../utils/config/selectors');
const {
  downloadPDFFile,
  downloadFileFromURL,
} = require('../utils/pdfDownloader');

async function getZhongDengInfo(page) {
  const registerNoToFiller = {};
  const zhongDengRows = await page
    .locator(SELECTORS.detail.zhongDengInfoRows)
    .all();

  for (let i = 0; i < zhongDengRows.length; i++) {
    const row = zhongDengRows[i];

    const registerNumber = await row.locator('td').nth(3).textContent();
    const fillerArchiveNumber = await row.locator('td').nth(10).textContent();

    registerNoToFiller[registerNumber.trim()] = fillerArchiveNumber.trim();

    // TODO: 找到  .el-table__fixed-right  这个下面 .file-list .list-item 的内容，下载为中登附件-textContext()
  }
  return registerNoToFiller;
}

// 获取中登附件
async function getZhongDengAttachment(
  page,
  downloadDir,
  zhongDengLatestFileNo,
) {
  const zhongDengRows = await page
    .locator(SELECTORS.detail.zhongDengInfoRows)
    .all();
  let currentFileNo = zhongDengLatestFileNo;
  for (let i = 0; i < zhongDengRows.length; i++) {
    const row = zhongDengRows[i];

    const fileTd = row.locator('td').nth(14);
    const fileLinks = fileTd.locator('.file-list .list-item');
    const linkLength = await fileLinks.count();
    for (let i = 0; i < linkLength; i++) {
      const fileLink = fileLinks.nth(i);
      const url = await fileLink.locator('a').getAttribute('href');
      const name = (await fileLink.textContent()).trim();
      currentFileNo += 1;
      await downloadFileFromURL(
        url,
        page,
        downloadDir,
        `4.${currentFileNo}-中登附件-${name}`,
      );
    }
  }
}

// 获取中登查重报告
async function getZhongDengReport(page, downloadDir) {
  logger.info(`开始获取中登查重报告`);

  let zhongDengLatestFileNo = 2; // 中登文件的编号记录 从 2 开始往后递增
  const registerNoToFiller = await getZhongDengInfo(page);
  const zhongDengRows = await page
    .locator(SELECTORS.detail.zhongDengChaChongRows)
    .all();

  for (let i = 0; i < zhongDengRows.length; i++) {
    const row = zhongDengRows[i];

    try {
      // 获取当前行的"更多"按钮的 aria-controls 属性
      // const moreButton = row.locator('button[aria-haspopup="list"]');
      // const dropdownId = await moreButton.getAttribute('aria-controls');

      // if (!dropdownId) {
      //   console.error(`第 ${i + 1} 行未找到下拉菜单ID`);
      //   continue;
      // }

      // await moreButton.waitFor({ state: 'visible', timeout: 10000 });

      // // 先 hover 触发下拉菜单
      // await moreButton.hover();
      // await page.waitForTimeout(1000);

      // // 然后点击
      // await moreButton.click();

      // await page.waitForTimeout(1000);

      //  // 监听新页面打开
      // const newPagePromise = page.context().waitForEvent('page');

      // // 使用精确的下拉菜单ID查找"查看结果"按钮
      // const viewResultButton = page.locator(
      //   `#${dropdownId} .el-dropdown-menu__item:has-text("查看结果")`,
      // );

      // await viewResultButton.waitFor({ state: 'visible', timeout: 5000 });
      // await viewResultButton.click();

      // // 等待新页面打开
      // const newPage = await newPagePromise;
      // await newPage.waitForLoadState('networkidle', { timeout: 15000 });

      // // 处理新页面中的中登报告数据
      // const latestFileNo = await processZhongDengReportPage(
      //   newPage,
      //   i + 1,
      //   registerNoToFiller,
      //   downloadDir,
      //   zhongDengLatestFileNo,
      // );

      // // 关闭新页面
      // await newPage.close();

      await getZhongDengAttachment(page, downloadDir, 1);
    } catch (error) {
      console.error(`处理第 ${i + 1} 行失败:`, error.message);
    }
  }
}

// 按优先级排序函数
function sortByPriority(data) {
  return data.sort((a, b) => {
    const getPriority = (assignee) => {
      if (assignee.includes('盛业商业保理')) return 1; // 最高优先级
      if (assignee.includes('国富保理')) return 3; // 最低优先级
      return 2; // 中等优先级
    };

    return getPriority(a.assignee) - getPriority(b.assignee);
  });
}

/**
 * 处理中登报告页面的表格数据
 * @param {Page} reportPage - 新打开的报告页面
 * @param {number} rowIndex - 行索引
 */
async function processZhongDengReportPage(
  reportPage,
  rowIndex,
  registerNoToFiller,
  downloadDir,
  zhongDengLatestFileNo,
) {
  try {
    logger.info(`开始处理第 ${rowIndex} 行的中登报告页面`);

    // 等待表格加载
    const tableSelector = '.el-table .el-table__body-wrapper tbody';
    await reportPage.waitForSelector(tableSelector, {
      state: 'visible',
      timeout: 10000,
    });

    // 获取分页控件和总页数
    const allData = [];
    const pageControl = reportPage.getByRole('list');
    const listItems = pageControl.getByRole('listitem');
    const totalPages = await listItems.count();

    logger.info(`总共 ${totalPages} 页数据需要处理`);

    // 第一遍：收集所有数据
    for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
      logger.info(`正在收集第 ${currentPage}/${totalPages} 页数据`);

      if (currentPage > 1) {
        const pageButton = pageControl
          .getByRole('listitem')
          .filter({ hasText: currentPage.toString() });
        await pageButton.click();
        await reportPage.waitForLoadState('networkidle', { timeout: 10000 });
        await reportPage.waitForTimeout(2000);
      }

      await reportPage.waitForSelector(tableSelector, {
        state: 'visible',
        timeout: 10000,
      });

      const reportRows = await reportPage.locator(`${tableSelector} tr`).all();

      for (let j = 0; j < reportRows.length; j++) {
        const reportRow = reportRows[j];
        const cells = reportRow.locator('td');
        const linkCell = cells.nth(3).locator('a').first();

        const no = await linkCell.textContent();
        const formatNo = no.trim();
        const assignee = (await cells.nth(6).textContent()).trim();
        const date = await cells.nth(4).textContent();
        const fillerArchiveNumber =
          registerNoToFiller[formatNo] || '无对应的填表人归档号';

        allData.push({
          formatNo,
          assignee,
          date: date.trim(),
          fillerArchiveNumber,
          pageNumber: currentPage,
          rowIndex: j,
        });
      }
    }

    // 收集完成后，回到第一页
    if (totalPages > 1) {
      logger.info('数据收集完成，回到第一页');
      const firstPageButton = pageControl
        .getByRole('listitem')
        .filter({ hasText: '1' });
      await firstPageButton.click();
      await reportPage.waitForLoadState('networkidle', { timeout: 10000 });
      await reportPage.waitForTimeout(2000);
    }

    // 按优先级排序并分配编号
    const sortedData = sortByPriority(allData);
    let currentFileNo = zhongDengLatestFileNo;

    // 为每个数据项分配文件编号
    sortedData.forEach((item) => {
      item.fileNo = currentFileNo;
      currentFileNo += 2; // 偶数递增
    });

    logger.info(`currentFileNo =====: ${currentFileNo}`);

    // 第二遍：按页面顺序下载文件
    for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
      logger.info(`正在下载第 ${currentPage}/${totalPages} 页文件`);

      if (currentPage > 1) {
        const pageButton = pageControl
          .getByRole('listitem')
          .filter({ hasText: currentPage.toString() });
        await pageButton.click();
        await reportPage.waitForLoadState('networkidle', { timeout: 10000 });
        await reportPage.waitForTimeout(2000);
      }

      await reportPage.waitForSelector(tableSelector, {
        state: 'visible',
        timeout: 10000,
      });

      const reportRows = await reportPage.locator(`${tableSelector} tr`).all();

      // 找到当前页面需要下载的数据
      const currentPageData = sortedData.filter(
        (item) => item.pageNumber === currentPage,
      );

      for (const dataItem of currentPageData) {
        const reportRow = reportRows[dataItem.rowIndex];
        const cells = reportRow.locator('td');
        const linkCell = cells.nth(3).locator('a').first();
        const fileName = `4.${dataItem.fileNo}-初始登记-${dataItem.date}-${dataItem.fillerArchiveNumber}-${dataItem.formatNo}`;
        logger.info(`下载文件: ${fileName} (受让人: ${dataItem.assignee})`);
        await downloadPDFFile(linkCell, reportPage, downloadDir, fileName);
      }
    }
    logger.info(`currentFileNo: ${currentFileNo}`);
    return currentFileNo - 2; // * -2 才是正确的最后一个的序号
  } catch (error) {
    console.error(`处理中登报告页面失败:`, error.message);
    throw error;
  }
}

module.exports = {
  getZhongDengReport,
};
